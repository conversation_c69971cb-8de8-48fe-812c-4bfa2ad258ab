import React, {useEffect, useState} from 'react';
import {View, StyleSheet, Text, ActivityIndicator} from 'react-native';
import {useDispatch} from 'react-redux';
import {useSelector} from 'react-redux';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import Lives from '../components/Lives';
import CountBadge from '../components/CountQuestions';
import ListAnswer from './components/ListAnswer';
import {AppDispatch, RootState} from '../../../redux/store/store';
import {SafeAreaView} from 'react-native-safe-area-context';
import GameOverModal from '../components/GameOverModel';
import WinnerModal from './components/WinnerModal';
import {BottomGame} from '../components/BottomGame';
import {useGameHook} from '../../../redux/hook/gameHook';
import {useSakuTBHook} from '../../../redux/hook/game/sakuTBHook';
import {loadGameQuestions, loadGameConfig,  calculateFinalScore} from '../../../redux/reducers/game/sakuTBReducer';
import ConfigAPI from '../../../Config/ConfigAPI';
import { useNavigation, useRoute } from '@react-navigation/native';

const StartSakuTB = () => {
  const dispatch: AppDispatch = useDispatch();
  const {isGameOver, messageGameOver, totalLives, currentLives} = useSelector(
    (state: RootState) => state.gameStore,
  );
  const {
    questionDone,
    totalQuestion,
    loading,
    error,
    initialized,
    currentQuestionIndex,
    configInitialized,
    maxLives,
    timeLimit,
    currentScore,
    bonusScore,
  } = useSelector(
    (state: RootState) => state.SakuTB,
  );
  const gameHook = useGameHook();
  const sakuTbHook = useSakuTBHook();
  //navigate
  const navigation = useNavigation<any>();
  //router param
  const route = useRoute<any>();
  const {competenceId} = route.params || {competenceId: '1'};

  // State cho Winner Modal
  const [showWinnerModal, setShowWinnerModal] = useState(false);

  // Load game config and questions on mount
  useEffect(() => {
    console.log('[StartSakuTB] Loading game config and questions...');

    // Load game config first
    dispatch(loadGameConfig({
      gameId: ConfigAPI.gameSakuTB
    }) as any);

    // Load game questions
    dispatch(loadGameQuestions({
      gameId: ConfigAPI.gameSakuTB,
      stage: 1,
      competenceId: competenceId
    }) as any);
  }, [dispatch, competenceId]);

  // Initialize game when data is loaded
  useEffect(() => {
    if (initialized && !loading && !error) {
      console.log('[StartSakuTB] Game data loaded, initializing...');
      restartGame();
    }
  }, [initialized, loading, error]);

  useEffect(() => {
    if (currentLives < 1) {
      gameOver('Thất bại rồi, làm lại nào');
    }
  }, [currentLives]);

  // Kiểm tra chiến thắng khi hoàn thành tất cả câu hỏi
  useEffect(() => {
    if (questionDone >= totalQuestion && totalQuestion > 0) {
      console.log('[StartSakuTB] All questions completed! Calculating final score...');

      // Tính điểm cuối cùng
      dispatch(calculateFinalScore());

      setTimeout(() => {
        setShowWinnerModal(true);
        gameHook.setData({stateName: 'isRunTime', value: false}); // Dừng timer
      }, 500); // Delay nhỏ để animation hoàn thành
    }
  }, [questionDone, totalQuestion, dispatch, gameHook]);

  const restartGame = () => {
    setShowWinnerModal(false);
    sakuTbHook.reset();

    // Sync global timer với SakuTB config
    if (configInitialized && timeLimit) {
      console.log(`[StartSakuTB] Syncing global timer with SakuTB config: ${timeLimit}s`);
      gameHook.setData({stateName: 'time', value: timeLimit});
      gameHook.setData({stateName: 'isRunTime', value: true});

      // Sync lives với SakuTB config
      if (maxLives) {
        gameHook.setData({stateName: 'totalLives', value: maxLives});
        gameHook.setData({stateName: 'currentLives', value: maxLives});
      }
    } else {
      // Fallback nếu config chưa load
      gameHook.resetGame();
    }
  };

  const gameOver = (message: string) => {
    gameHook.gameOver(message);
  };

  // Show loading state
  if (loading) {
    return (
      <SafeAreaView style={{flex: 1}}>
        <View style={[styles.container, styles.centerContent]}>
          <ActivityIndicator size="large" color="#112164" />
          <Text style={styles.loadingText}>Đang tải câu hỏi...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Show error state
  if (error && !initialized) {
    return (
      <SafeAreaView style={{flex: 1}}>
        <View style={[styles.container, styles.centerContent]}>
          <Text style={styles.errorText}>Lỗi tải dữ liệu: {error}</Text>
          <Text style={styles.errorSubText}>Đang sử dụng dữ liệu dự phòng...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Show not initialized state
  if (!initialized) {
    return (
      <SafeAreaView style={{flex: 1}}>
        <View style={[styles.container, styles.centerContent]}>
          <Text style={styles.errorText}>Chưa có dữ liệu game</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{flex: 1}}>
      <View
        style={{
          backgroundColor: '#F1D1A6',
          alignItems: 'center',
          flex: 1,
        }}>
        <View style={styles.container}>
          <HeadGame timeOut={() => gameOver('Hết giờ rồi, làm lại nào')} />
          <LineProgressBar
            progress={(questionDone / totalQuestion) * 100}></LineProgressBar>
          <View
            style={{
              width: '100%',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <Lives totalLives={totalLives} currentLives={currentLives}></Lives>
            <CountBadge
              current={currentQuestionIndex + 1}
              total={totalQuestion}></CountBadge>
          </View>



          {/* Danh sách các đáp án */}
          <View style={{marginTop: 16}}>
            <ListAnswer></ListAnswer>
          </View>

          {/* Bottom game */}
          <View style={{position: 'absolute', bottom: 6}}>
            <BottomGame
              resetGame={restartGame}
              backGame={() => {navigation.goBack()}}
              pauseGame={() => {}}
              volumeGame={() => {}}></BottomGame>
          </View>
        </View>
      </View>
      {/* Model Thất bại */}
      <GameOverModal
        visible={isGameOver}
        onClose={() => {}}
        restartGame={restartGame}
        message={messageGameOver}
        isTimeOut={false}
      />

      {/* Winner Modal */}
      <WinnerModal
        visible={showWinnerModal}
        onClose={() => setShowWinnerModal(false)}
        restartGame={restartGame}
        score={currentScore}
        totalLives={maxLives}
        currentLives={currentLives}
        bonusScore={bonusScore}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
    marginHorizontal: 16,
    marginTop: 12,
    alignItems: 'center',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#112164',
    marginTop: 16,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#FF5722',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  errorSubText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
  },
  questionInfo: {
    backgroundColor: '#FCF8E8',
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
    width: '100%',
    alignItems: 'center',
  },
  questionNumber: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#112164',
    marginBottom: 4,
  },
  questionContent: {
    fontSize: 16,
    color: '#112164',
    textAlign: 'center',
    lineHeight: 22,
  },
});

export default StartSakuTB;
